# Build Stage
FROM openjdk:17-jdk-slim AS build

WORKDIR /app

# Install Maven as a fallback
RUN apt-get update && apt-get install -y maven && rm -rf /var/lib/apt/lists/*

# Copy Maven Wrapper and project files
COPY pom.xml mvnw ./
COPY .mvn .mvn
RUN chmod +x mvnw

# Copy source code
COPY src src

# Build the application
RUN ./mvnw clean package -DskipTests || mvn clean package -DskipTests

# Runtime Stage
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY --from=build /app/target/quizApp-0.0.1-SNAPSHOT.jar /app/quizApp.jar

EXPOSE 8080

CMD ["java", "-jar", "/app/quizApp.jar"]