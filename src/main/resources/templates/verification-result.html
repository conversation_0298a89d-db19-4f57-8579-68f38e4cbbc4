<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification - Quiz App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            margin: 20px;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .success-icon {
            color: #28a745;
        }
        
        .error-icon {
            color: #dc3545;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .success-title {
            color: #28a745;
        }
        
        .error-title {
            color: #dc3545;
        }
        
        .message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #666;
        }
        
        .user-greeting {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .user-greeting strong {
            color: #667eea;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #999;
            font-size: 0.9rem;
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Success State -->
        <div th:if="${success}">
            <div class="icon success-icon">✓</div>
            <h1 class="success-title">Verification Successful!</h1>
            <div class="user-greeting" th:if="${userName}">
                Welcome, <strong th:text="${userName}">User</strong>!
            </div>
            <p class="message" th:text="${message}">Your account has been verified successfully!</p>
        </div>
        
        <!-- Error State -->
        <div th:unless="${success}">
            <div class="icon error-icon">✗</div>
            <h1 class="error-title">Verification Failed</h1>
            <p class="message" th:text="${message}">There was an issue verifying your account.</p>
        </div>
        
        <div class="footer">
            <p>© 2024 <span th:text="${appName}">Quiz App</span>. All rights reserved.</p>
            <p>If you have any questions, please contact our support team.</p>
        </div>
    </div>
    
    <script>
        // Auto-close after successful verification (optional)
        if (window.location.search.includes('success=true')) {
            setTimeout(function() {
                if (confirm('Verification successful! Close this window?')) {
                    window.close();
                }
            }, 3000);
        }
    </script>
</body>
</html>
