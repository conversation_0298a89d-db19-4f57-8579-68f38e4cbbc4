<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Account</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-text {
            font-size: 18px;
            margin-bottom: 20px;
            color: #555;
        }
        .verification-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .verification-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .alternative-text {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #eee;
        }
        .security-note {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            font-size: 14px;
            color: #856404;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0 10px;
            }
            .content, .header {
                padding: 20px;
            }
            .verification-button {
                display: block;
                text-align: center;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1 th:text="${appName}">Quiz App</h1>
    </div>

    <div class="content">
        <div class="welcome-text">
            Hi <span th:text="${userName}">User</span>,
        </div>

        <p>Welcome to <span th:text="${appName}">Quiz App</span>! We're excited to have you on board.</p>

        <p>To complete your registration and start using your account, please verify your email address by clicking the button below:</p>

        <div style="text-align: center; margin: 30px 0;">
            <a th:href="${verificationUrl}" class="verification-button">Verify My Account</a>
        </div>

        <div class="alternative-text">
            <strong>Can't click the button?</strong><br>
            Copy and paste this link into your browser:<br>
            <a th:href="${verificationUrl}" th:text="${verificationUrl}" style="color: #667eea; word-break: break-all;">Verification Link</a>
        </div>

        <div class="security-note">
            <strong>Security Note:</strong> This verification link will expire in 24 hours for your security.
            If you didn't create an account with us, please ignore this email.
        </div>
    </div>

    <div class="footer">
        <p>© 2024 <span th:text="${appName}">Quiz App</span>. All rights reserved.</p>
        <p>If you have any questions, contact <NAME_EMAIL></p>
    </div>
</div>
</body>
</html>