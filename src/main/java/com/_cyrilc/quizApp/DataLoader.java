//package com._cyrilc.quizApp;
//
//import com._cyrilc.quizApp.entity.Question;
//import com._cyrilc.quizApp.repository.QuestionRepository;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//
//@Component
//public class DataLoader {
//
//    @Bean
//    CommandLineRunner loadData(QuestionRepository questionRepository) {
//        return args -> {
//            questionRepository.save(new Question(null, "What is the capital of France?", new String[]{"Paris", "London", "Berlin", "Madrid"}, "Paris"));
//            questionRepository.save(new Question(null, "What is 2 + 2?", new String[]{"2", "4", "6", "8"}, "4"));
//            questionRepository.save(new Question(null, "Which planet is known as the Red Planet?", new String[]{"Venus", "Mars", "Jupiter", "Saturn"}, "Mars"));
//            questionRepository.save(new Question(null, "Who wrote '<PERSON> and Juliet'?", new String[]{"<PERSON>", "<PERSON>", "<PERSON>", "Mark Twain"}, "<PERSON>"));
//            questionRepository.save(new Question(null, "What is the largest mammal in the world?", new String[]{"Elephant", "Blue Whale", "Giraffe", "Hippopotamus"}, "Blue Whale"));
//            questionRepository.save(new Question(null, "Which element has the chemical symbol 'O'?", new String[]{"Gold", "Oxygen", "Iron", "Silver"}, "Oxygen"));
//            questionRepository.save(new Question(null, "In which year did World War II end?", new String[]{"1942", "1945", "1939", "1948"}, "1945"));
//            questionRepository.save(new Question(null, "What is the capital of Japan?", new String[]{"Tokyo", "Kyoto", "Osaka", "Hiroshima"}, "Tokyo"));
//            questionRepository.save(new Question(null, "Which gas makes up 78% of Earth's atmosphere?", new String[]{"Oxygen", "Carbon Dioxide", "Nitrogen", "Helium"}, "Nitrogen"));
//            questionRepository.save(new Question(null, "Who painted the Mona Lisa?", new String[]{"Vincent van Gogh", "Leonardo da Vinci", "Pablo Picasso", "Claude Monet"}, "Leonardo da Vinci"));
//            questionRepository.save(new Question(null, "What is the square root of 16?", new String[]{"2", "4", "8", "16"}, "4"));
//            questionRepository.save(new Question(null, "Which country hosted the 2016 Summer Olympics?", new String[]{"China", "Brazil", "UK", "USA"}, "Brazil"));
//            questionRepository.save(new Question(null, "What is the chemical formula for water?", new String[]{"H2O", "CO2", "O2", "H2SO4"}, "H2O"));
//            questionRepository.save(new Question(null, "Who discovered penicillin?", new String[]{"Alexander Fleming", "Marie Curie", "Albert Einstein", "Isaac Newton"}, "Alexander Fleming"));
//            questionRepository.save(new Question(null, "What is the longest river in the world?", new String[]{"Amazon", "Nile", "Yangtze", "Mississippi"}, "Nile"));
//            questionRepository.save(new Question(null, "Which planet is closest to the Sun?", new String[]{"Venus", "Mercury", "Earth", "Mars"}, "Mercury"));
//            questionRepository.save(new Question(null, "Who was the first president of the United States?", new String[]{"Abraham Lincoln", "George Washington", "Thomas Jefferson", "John Adams"}, "George Washington"));
//            questionRepository.save(new Question(null, "What is the powerhouse of the cell?", new String[]{"Nucleus", "Mitochondria", "Ribosome", "Golgi Apparatus"}, "Mitochondria"));
//            questionRepository.save(new Question(null, "Which country is known as the Land of the Rising Sun?", new String[]{"China", "Japan", "Korea", "Thailand"}, "Japan"));
//            questionRepository.save(new Question(null, "What is 5 x 6?", new String[]{"25", "30", "35", "40"}, "30"));
//            questionRepository.save(new Question(null, "Who directed the movie 'Titanic'?", new String[]{"Steven Spielberg", "James Cameron", "Christopher Nolan", "Quentin Tarantino"}, "James Cameron"));
//            questionRepository.save(new Question(null, "What is the capital of Australia?", new String[]{"Sydney", "Melbourne", "Canberra", "Brisbane"}, "Canberra"));
//            questionRepository.save(new Question(null, "Which gas is used in balloons to make them float?", new String[]{"Oxygen", "Hydrogen", "Helium", "Carbon Dioxide"}, "Helium"));
//            questionRepository.save(new Question(null, "Who wrote 'Pride and Prejudice'?", new String[]{"Jane Austen", "Emily Brontë", "Charlotte Brontë", "Virginia Woolf"}, "Jane Austen"));
//            questionRepository.save(new Question(null, "What is the largest planet in our solar system?", new String[]{"Earth", "Jupiter", "Saturn", "Uranus"}, "Jupiter"));
//            questionRepository.save(new Question(null, "In which year did the Titanic sink?", new String[]{"1905", "1912", "1920", "1930"}, "1912"));
//            questionRepository.save(new Question(null, "What is the chemical symbol for gold?", new String[]{"Au", "Ag", "Fe", "Cu"}, "Au"));
//            questionRepository.save(new Question(null, "Which animal is known as man's best friend?", new String[]{"Cat", "Dog", "Horse", "Rabbit"}, "Dog"));
//            questionRepository.save(new Question(null, "What is the capital of Brazil?", new String[]{"Rio de Janeiro", "São Paulo", "Brasília", "Salvador"}, "Brasília"));
//            questionRepository.save(new Question(null, "Who developed the theory of relativity?", new String[]{"Isaac Newton", "Albert Einstein", "Galileo Galilei", "Stephen Hawking"}, "Albert Einstein"));
//            questionRepository.save(new Question(null, "What is 10 divided by 2?", new String[]{"2", "4", "5", "10"}, "5"));
//            questionRepository.save(new Question(null, "Which country is famous for the Eiffel Tower?", new String[]{"Italy", "France", "Spain", "Germany"}, "France"));
//            questionRepository.save(new Question(null, "What is the primary source of energy for Earth's climate system?", new String[]{"Moon", "Sun", "Wind", "Ocean"}, "Sun"));
//            questionRepository.save(new Question(null, "Who was the first woman to win a Nobel Prize?", new String[]{"Marie Curie", "Mother Teresa", "Rosalind Franklin", "Ada Lovelace"}, "Marie Curie"));
//            questionRepository.save(new Question(null, "What is the capital of Canada?", new String[]{"Toronto", "Vancouver", "Ottawa", "Montreal"}, "Ottawa"));
//            questionRepository.save(new Question(null, "Which element is essential for human bones?", new String[]{"Iron", "Calcium", "Magnesium", "Zinc"}, "Calcium"));
//            questionRepository.save(new Question(null, "What is the smallest prime number?", new String[]{"1", "2", "3", "5"}, "2"));
//            questionRepository.save(new Question(null, "Which ocean is the largest?", new String[]{"Atlantic", "Indian", "Arctic", "Pacific"}, "Pacific"));
//            questionRepository.save(new Question(null, "Who painted 'Starry Night'?", new String[]{"Claude Monet", "Vincent van Gogh", "Pablo Picasso", "Edvard Munch"}, "Vincent van Gogh"));
//            questionRepository.save(new Question(null, "What is the capital of India?", new String[]{"Mumbai", "Delhi", "Kolkata", "Chennai"}, "Delhi"));
//            questionRepository.save(new Question(null, "Which gas is essential for photosynthesis?", new String[]{"Oxygen", "Carbon Dioxide", "Nitrogen", "Helium"}, "Carbon Dioxide"));
//            questionRepository.save(new Question(null, "Who was the first man to walk on the moon?", new String[]{"Buzz Aldrin", "Neil Armstrong", "Yuri Gagarin", "John Glenn"}, "Neil Armstrong"));
//            questionRepository.save(new Question(null, "What is 7 x 8?", new String[]{"54", "56", "64", "72"}, "56"));
//            questionRepository.save(new Question(null, "Which country is known for the Great Wall?", new String[]{"Japan", "China", "India", "Korea"}, "China"));
//            questionRepository.save(new Question(null, "What is the boiling point of water in Celsius?", new String[]{"0", "50", "100", "150"}, "100"));
//            questionRepository.save(new Question(null, "Who wrote 'The Great Gatsby'?", new String[]{"F. Scott Fitzgerald", "Ernest Hemingway", "John Steinbeck", "William Faulkner"}, "F. Scott Fitzgerald"));
//            questionRepository.save(new Question(null, "What is the capital of Russia?", new String[]{"Moscow", "St. Petersburg", "Kiev", "Minsk"}, "Moscow"));
//            questionRepository.save(new Question(null, "Which animal is the fastest land animal?", new String[]{"Lion", "Cheetah", "Horse", "Elephant"}, "Cheetah"));
//            questionRepository.save(new Question(null, "What is the chemical symbol for iron?", new String[]{"Fe", "Ir", "In", "I"}, "Fe"));
//            questionRepository.save(new Question(null, "Which continent is the Sahara Desert located on?", new String[]{"Asia", "Africa", "Australia", "South America"}, "Africa"));
//        };
//    }
//}