package com._cyrilc.quizApp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
public class WebConfig implements WebMvcConfigurer {
   @Override
   public void addCorsMappings(CorsRegistry registry) {
       registry.addMapping("/**")
               .allowedOrigins(
                   "http://localhost:3000",
                   "http://localhost:5173"
               )
               .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
               .allowedHeaders("*")
               .allowCredentials(true)
               .maxAge(3600);
   }
}
