package com._cyrilc.quizApp.controller;

import com._cyrilc.quizApp.dto.HistoryResponse;
import com._cyrilc.quizApp.dto.MessageResponse;
import com._cyrilc.quizApp.dto.QuizResponse;
import com._cyrilc.quizApp.dto.ScoreResponse;
import com._cyrilc.quizApp.entity.Question;
import com._cyrilc.quizApp.entity.ScoreHistory;
import com._cyrilc.quizApp.entity.User;
import com._cyrilc.quizApp.repository.QuestionRepository;
import com._cyrilc.quizApp.repository.ScoreHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/quiz")
public class QuizController {

    @Autowired
    private QuestionRepository questionRepository;
    @Autowired private ScoreHistoryRepository scoreHistoryRepository;

    @GetMapping("/questions")
    public ResponseEntity<QuizResponse> getQuestions() {
        try {
            List<Question> allQuestions = questionRepository.findAll();

            if (allQuestions.isEmpty()) {
                return ResponseEntity.ok(new QuizResponse("No questions available", false));
            }

            Collections.shuffle(allQuestions);
            List<Question> selectedQuestions = allQuestions.subList(0, Math.min(10, allQuestions.size()));

            return ResponseEntity.ok(new QuizResponse(selectedQuestions));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(new QuizResponse("Failed to retrieve questions: " + e.getMessage(), false));
        }
    }

    @PostMapping("/submit")
    public ResponseEntity<ScoreResponse> submit(@RequestBody Map<Long, String> answers, @AuthenticationPrincipal User user) {
        try {
            if (answers == null || answers.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ScoreResponse("No answers provided", false));
            }

            int score = 0;
            int totalQuestions = answers.size();

            for (Map.Entry<Long, String> entry : answers.entrySet()) {
                Question question = questionRepository.findById(entry.getKey()).orElse(null);
                if (question != null && question.getCorrectAnswer().equals(entry.getValue())) {
                    score++;
                }
            }

            ScoreHistory history = new ScoreHistory();
            history.setUser(user);
            history.setScore(score);
            history.setTimestamp(LocalDateTime.now());
            scoreHistoryRepository.save(history);

            List<ScoreHistory> pastScores = scoreHistoryRepository.findByUser(user);
            return ResponseEntity.ok(new ScoreResponse(score, totalQuestions, pastScores));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(new ScoreResponse("Failed to submit quiz: " + e.getMessage(), false));
        }
    }

    @GetMapping("/history")
    public ResponseEntity<HistoryResponse> getHistory(@AuthenticationPrincipal User user) {
        try {
            List<ScoreHistory> history = scoreHistoryRepository.findByUser(user);

            if (history.isEmpty()) {
                return ResponseEntity.ok(new HistoryResponse("No quiz history found", true));
            }

            return ResponseEntity.ok(new HistoryResponse(history));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(new HistoryResponse("Failed to retrieve history: " + e.getMessage(), false));
        }
    }
}
