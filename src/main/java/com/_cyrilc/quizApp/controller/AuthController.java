package com._cyrilc.quizApp.controller;

import com._cyrilc.quizApp.dto.AuthResponse;
import com._cyrilc.quizApp.dto.LoginRequest;
import com._cyrilc.quizApp.dto.MessageResponse;
import com._cyrilc.quizApp.entity.User;
import com._cyrilc.quizApp.entity.VerificationToken;
import com._cyrilc.quizApp.repository.UserRepository;
import com._cyrilc.quizApp.repository.VerificationTokenRepository;
import com._cyrilc.quizApp.service.EmailService;
import com._cyrilc.quizApp.util.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/auth")
public class AuthController {
    @Autowired private UserRepository userRepository;
    @Autowired private VerificationTokenRepository tokenRepository;
    @Autowired private EmailService emailService;
    @Autowired private PasswordEncoder passwordEncoder;
    @Autowired private AuthenticationManager authenticationManager;
    @Autowired private JwtUtils jwtUtils;

    @PostMapping("/register")
    public ResponseEntity<MessageResponse> register(@RequestBody User user) {
        try {
            if (userRepository.findByEmail(user.getEmail()) != null ) {
                return ResponseEntity.badRequest()
                    .body(MessageResponse.error("Email already exists"));
            }

            if (userRepository.findByUsername(user.getUsername()) != null) {
                return ResponseEntity.badRequest()
                    .body(MessageResponse.error("Username already exists"));
            }

            user.setPassword(passwordEncoder.encode(user.getPassword()));
            user.setVerified(false);
            userRepository.save(user);

            String token = UUID.randomUUID().toString();
            VerificationToken verificationToken = new VerificationToken();
            verificationToken.setToken(token);
            verificationToken.setUser(user);
            verificationToken.setExpiryDate(LocalDateTime.now().plusHours(24));
            tokenRepository.save(verificationToken);

            emailService.sendVerificationEmail(user, token);

            return ResponseEntity.ok(MessageResponse.success("User registered successfully. Please check your email for verification."));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(MessageResponse.error("Registration failed: " + e.getMessage()));
        }
    }

    @GetMapping("/verify")
    public ResponseEntity<MessageResponse> verify(@RequestParam String token) {
        try {
            VerificationToken verificationToken = tokenRepository.findByToken(token);
            if (verificationToken == null || verificationToken.getExpiryDate().isBefore(LocalDateTime.now())) {
                return ResponseEntity.badRequest()
                    .body(MessageResponse.error("Invalid or expired verification token"));
            }

            User user = verificationToken.getUser();
            user.setVerified(true);
            userRepository.save(user);
            tokenRepository.delete(verificationToken);

            return ResponseEntity.ok(MessageResponse.success("Account verified successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(MessageResponse.error("Verification failed: " + e.getMessage()));
        }
    }

    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request) {
        try {
            Authentication auth = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );

            String token = jwtUtils.generateToken(auth.getName());
            return ResponseEntity.ok(new AuthResponse(
                "Login successful",
                true,
                token,
                auth.getName()
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new AuthResponse("Invalid username or password", false));
        }
    }

    @GetMapping("/profile")
    public User getProfile(@AuthenticationPrincipal User user) {
        return user;
    }
}
