package com._cyrilc.quizApp.controller;

import com._cyrilc.quizApp.entity.User;
import com._cyrilc.quizApp.entity.VerificationToken;
import com._cyrilc.quizApp.repository.UserRepository;
import com._cyrilc.quizApp.repository.VerificationTokenRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;

@Controller
public class WebController {

    @Autowired
    private VerificationTokenRepository tokenRepository;

    @Autowired
    private UserRepository userRepository;

    @Value("${app.name:Quiz App}")
    private String appName;

    @GetMapping("/verify")
    public String verifyAccount(@RequestParam String token, Model model) {
        try {
            VerificationToken verificationToken = tokenRepository.findByToken(token);
            
            if (verificationToken == null || verificationToken.getExpiryDate().isBefore(LocalDateTime.now())) {
                model.addAttribute("success", false);
                model.addAttribute("message", "Invalid or expired verification token");
                model.addAttribute("appName", appName);
                return "verification-result";
            }

            User user = verificationToken.getUser();
            user.setVerified(true);
            userRepository.save(user);
            tokenRepository.delete(verificationToken);

            model.addAttribute("success", true);
            model.addAttribute("message", "Your account has been verified successfully!");
            model.addAttribute("userName", user.getUsername());
            model.addAttribute("appName", appName);
            
            return "verification-result";
            
        } catch (Exception e) {
            model.addAttribute("success", false);
            model.addAttribute("message", "Verification failed: " + e.getMessage());
            model.addAttribute("appName", appName);
            return "verification-result";
        }
    }
}
