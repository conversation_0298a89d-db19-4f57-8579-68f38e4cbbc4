package com._cyrilc.quizApp.dto;

import com._cyrilc.quizApp.entity.Question;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDto {
    private Long id;
    private String text;
    private String[] options;

    // Constructor to convert from Question entity (excluding correctAnswer)
    public QuestionDto(Question question) {
        this.id = question.getId();
        this.text = question.getText();
        this.options = question.getOptions();
    }
}
