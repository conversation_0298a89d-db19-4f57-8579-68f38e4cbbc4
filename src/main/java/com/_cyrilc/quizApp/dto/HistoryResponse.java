package com._cyrilc.quizApp.dto;

import com._cyrilc.quizApp.entity.ScoreHistory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HistoryResponse {
    private String message;
    private boolean success;
    private List<ScoreHistory> data;
    private int totalAttempts;

    // Constructor for successful history retrieval
    public HistoryResponse(List<ScoreHistory> data) {
        this.message = "Quiz history retrieved successfully";
        this.success = true;
        this.data = data;
        this.totalAttempts = data.size();
    }

    // Constructor for error responses
    public HistoryResponse(String message, boolean success) {
        this.message = message;
        this.success = success;
        this.totalAttempts = 0;
    }
}
