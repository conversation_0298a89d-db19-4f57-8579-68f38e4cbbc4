package com._cyrilc.quizApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {
    private String message;
    private boolean success;
    private String token;
    private String username;

    // Constructor for success messages without token
    public AuthResponse(String message, boolean success) {
        this.message = message;
        this.success = success;
    }
}
