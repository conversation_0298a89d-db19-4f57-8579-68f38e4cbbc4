package com._cyrilc.quizApp.dto;

import com._cyrilc.quizApp.entity.ScoreHistory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreResponse {
    private String message;
    private boolean success;
    private int score;
    private int totalQuestions;
    private double percentage;
    private LocalDateTime timestamp;
    private List<ScoreHistory> history;

    // Constructor for quiz submission response
    public ScoreResponse(int score, int totalQuestions, List<ScoreHistory> history) {
        this.message = "Quiz submitted successfully";
        this.success = true;
        this.score = score;
        this.totalQuestions = totalQuestions;
        this.percentage = totalQuestions > 0 ? (double) score / totalQuestions * 100 : 0;
        this.timestamp = LocalDateTime.now();
        this.history = history;
    }

    // Constructor for error responses
    public ScoreResponse(String message, boolean success) {
        this.message = message;
        this.success = success;
    }
}
